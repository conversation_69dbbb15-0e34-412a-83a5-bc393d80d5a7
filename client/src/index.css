@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  /* Theme: Nebula Dark - Matching the reference image */

  /* Main chat area - very dark with subtle warmth */
  --background: 0 0% 4%;
  --foreground: 0 0% 98%;
  --muted: 0 0% 8%;
  --muted-foreground: 0 0% 60%;
  --popover: 0 0% 6%;
  --popover-foreground: 0 0% 98%;
  --card: 0 0% 6%;
  --card-foreground: 0 0% 98%;
  --border: 320 12% 15%;
  --input: 0 0% 8%;
  --primary: 320 100% 70%;
  --primary-foreground: 0 0% 98%;
  --secondary: 0 0% 10%;
  --secondary-foreground: 0 0% 98%;
  --accent: 320 100% 70%;
  --accent-foreground: 0 0% 98%;
  --destructive: 0 62.8% 30.6%;
  --destructive-foreground: 0 0% 98%;
  --ring: 320 15% 25%;
  --radius: 0.5rem;

  /* Hover states - consistent across all components */
  --hover-bg: var(--primary) / 0.1;
  --hover-border: var(--primary) / 0.4;
  --hover-shadow: var(--primary) / 0.2;

  /* Sidebar - darker with subtle purple tint for distinction */
  --sidebar-background: 320 8% 3%;
  --sidebar-foreground: 0 0% 98%;
  --sidebar-primary: 320 100% 70%;
  --sidebar-primary-foreground: 0 0% 98%;
  --sidebar-accent: 320 12% 6%;
  --sidebar-accent-foreground: 0 0% 98%;
  --sidebar-border: 320 12% 18%;
  --sidebar-ring: 320 15% 25%;

  /* Chart colors - Nebula Dark theme */
  --chart-1: 320 100% 70%;
  --chart-2: 300 80% 65%;
  --chart-3: 340 90% 75%;
  --chart-4: 280 70% 60%;
  --chart-5: 260 85% 65%;

  /* Chain colors - Nebula Dark theme */
  --magenta-500: #ff00ff;
  --purple-500: #a855f7;
  --pink-500: #ec4899;
  --magenta-400: #ff66ff;
  --purple-400: #c084fc;
}

.light {
  /* Theme: Nebula Light - Clean and bright */

  /* Main chat area - light with subtle warmth */
  --background: 0 0% 98%;
  --foreground: 0 0% 8%;
  --muted: 0 0% 94%;
  --muted-foreground: 0 0% 45%;
  --popover: 0 0% 100%;
  --popover-foreground: 0 0% 8%;
  --card: 0 0% 100%;
  --card-foreground: 0 0% 8%;
  --border: 320 30% 80%;
  --input: 0 0% 96%;
  --primary: 320 100% 50%;
  --primary-foreground: 0 0% 98%;
  --secondary: 0 0% 92%;
  --secondary-foreground: 0 0% 8%;
  --accent: 320 100% 50%;
  --accent-foreground: 0 0% 98%;
  --destructive: 0 84.2% 60.2%;
  --destructive-foreground: 0 0% 98%;
  --ring: 320 100% 50%;

  /* Hover states - consistent across all components */
  --hover-bg: var(--primary) / 0.15;
  --hover-border: var(--primary) / 0.6;
  --hover-shadow: var(--primary) / 0.2;

  /* Sidebar - slightly darker with subtle purple tint for distinction */
  --sidebar-background: 320 15% 92%;
  --sidebar-foreground: 0 0% 8%;
  --sidebar-primary: 320 100% 50%;
  --sidebar-primary-foreground: 0 0% 98%;
  --sidebar-accent: 320 20% 88%;
  --sidebar-accent-foreground: 0 0% 8%;
  --sidebar-border: 320 30% 75%;
  --sidebar-ring: 320 100% 50%;

  /* Chart colors - Nebula Light theme */
  --chart-1: 320 100% 50%;
  --chart-2: 300 70% 55%;
  --chart-3: 340 80% 60%;
  --chart-4: 280 60% 50%;
  --chart-5: 260 75% 55%;

  /* Chain colors - Nebula Light theme */
  --magenta-500: #a277a8;
  --purple-500: #8b5cf6;
  --pink-500: #a16382;
  --magenta-400: #e879f9;
  --purple-400: #a78bfa;
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply font-sans antialiased bg-background text-foreground;
  }
}

/* Hide scrollbar utility */
.scrollbar-hide {
  -ms-overflow-style: none;  /* Internet Explorer 10+ */
  scrollbar-width: none;  /* Firefox */
}

.scrollbar-hide::-webkit-scrollbar {
  display: none;  /* Safari and Chrome */
}

/* Custom scrollbar for chat container */
.chat-container::-webkit-scrollbar {
  width: 6px;
}

.chat-container::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.1);
}

.chat-container::-webkit-scrollbar-thumb {
  background: #271122;
  border-radius: 3px;
  transition: background 0.2s ease;
}

.chat-container::-webkit-scrollbar-thumb:hover {
  background: #3f253d;
}

/* Custom scrollbar for sidebar chat list */
.sidebar-chat-list {
  scrollbar-width: thin;
  scrollbar-color: rgba(255, 255, 255, 0.1) transparent;
  position: relative;
}

.sidebar-chat-list::-webkit-scrollbar {
  width: 8px;
}

.sidebar-chat-list::-webkit-scrollbar-track {
  background: transparent;
  border-radius: 4px;
}

.sidebar-chat-list::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.08);
  border-radius: 4px;
  transition: all 0.2s ease;
}

.sidebar-chat-list::-webkit-scrollbar-thumb:hover {
  background: rgba(255, 255, 255, 0.2);
}

.sidebar-chat-list:hover::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.12);
}

/* Fade effect for scrollable content */
.sidebar-chat-list::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 8px;
  background: linear-gradient(to bottom, rgba(20, 21, 30, 0.8), transparent);
  pointer-events: none;
  z-index: 1;
  opacity: 0;
  transition: opacity 0.2s ease;
}

.sidebar-chat-list::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 8px;
  background: linear-gradient(to top, rgba(20, 21, 30, 0.8), transparent);
  pointer-events: none;
  z-index: 1;
  opacity: 0;
  transition: opacity 0.2s ease;
}

.sidebar-chat-list.scrollable::before,
.sidebar-chat-list.scrollable::after {
  opacity: 1;
}

/* Animations */
.fade-in {
  animation: fadeIn 0.5s ease-in;
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

.pulse {
  animation: pulse 1.5s infinite;
}

@keyframes pulse {
  0% { opacity: 0.6; }
  50% { opacity: 1; }
  100% { opacity: 0.6; }
}

/* Theme: Nebula Dark - Enhanced UI Components */
.nebula-card {
  @apply bg-card border border-border p-4 backdrop-blur-sm;
  border-radius: var(--radius);
  background: linear-gradient(135deg, rgba(10, 10, 15, 0.95), rgba(15, 15, 20, 0.9));
  border: 1px solid hsl(var(--border) / 0.3);
  box-shadow: 0 8px 32px rgba(255, 0, 255, 0.1), 0 4px 16px rgba(0, 0, 0, 0.5);
}

.nebula-transaction-card {
  @apply bg-card border border-border p-4 backdrop-blur-sm mb-4;
  border-radius: var(--radius);
  background: linear-gradient(135deg, rgba(10, 10, 15, 0.95), rgba(15, 15, 20, 0.9));
  border: 1px solid hsl(var(--primary) / 0.3);
  box-shadow: 0 8px 32px rgba(255, 0, 255, 0.1), 0 4px 16px rgba(0, 0, 0, 0.5);
  max-width: 400px;
  width: 100%;
}

.nebula-transaction-row {
  @apply flex items-center justify-between py-2 border-b border-border/50 last:border-0;
}

.nebula-transaction-label {
  @apply text-sm text-muted-foreground;
}

.nebula-transaction-value {
  @apply text-sm font-medium text-foreground;
}

.nebula-icon-bg {
  @apply flex items-center justify-center;
  border-radius: var(--radius);
  background: linear-gradient(135deg, hsl(var(--primary) / 0.6), hsl(var(--primary) / 0.3));
  border: 1px solid hsl(var(--primary) / 0.4);
  box-shadow: 0 4px 12px hsl(var(--primary) / 0.2);
}

.nebula-action-button {
  @apply flex items-center justify-center gap-2 px-4 py-2 text-sm font-medium transition-all duration-200;
  border-radius: var(--radius);
  background: linear-gradient(135deg, hsl(var(--primary) / 0.4), hsl(var(--primary) / 0.2));
  border: 1px solid hsl(var(--primary) / 0.5);
  box-shadow: 0 4px 12px hsl(var(--primary) / 0.2);
}

.nebula-action-button:hover {
  background: linear-gradient(135deg, hsl(var(--primary) / 0.6), hsl(var(--primary) / 0.4));
  border: 1px solid hsl(var(--primary) / 0.7);
  box-shadow: 0 6px 20px hsl(var(--primary) / 0.3);
  transform: translateY(-1px);
}

.nebula-action-button:disabled {
  background: linear-gradient(135deg, hsl(var(--muted) / 0.3), hsl(var(--muted) / 0.2));
  border: 1px solid hsl(var(--border));
  box-shadow: none;
  transform: none;
  opacity: 0.6;
}

.nebula-suggestion-chip {
  @apply px-3 py-1.5 font-medium inline-flex items-center gap-1.5 transition-all duration-200;
  font-size: 0.56rem; /* 30% smaller than previous 0.8rem */
  border-radius: 9999px; /* Full rounded pill shape */
  background: hsl(var(--muted) / 0.3);
  border: 1px solid hsl(var(--border) / 0.3);
  color: hsl(var(--foreground) / 0.8);
  backdrop-filter: blur(10px);
}

.nebula-suggestion-chip:hover {
  background: hsl(var(--hover-bg));
  border: 1px solid hsl(var(--hover-border));
  color: hsl(var(--foreground));
  box-shadow: 0 4px 12px hsl(var(--hover-shadow));
  transform: translateY(-1px);
}

/* Light theme specific adjustments for suggestion chips */
.light .nebula-suggestion-chip {
  background: hsl(var(--muted) / 0.6);
  border: 1px solid hsl(var(--border) / 0.5);
  color: hsl(var(--foreground) / 0.9);
}

/* Enhanced input styling with theme borders */
.theme-input {
  border: 1px solid hsl(var(--border));
  border-radius: var(--radius);
  transition: all 0.2s ease;
}

.theme-input:focus {
  border-color: hsl(var(--primary) / 0.4);
  box-shadow: 0 0 0 2px hsl(var(--ring) / 0.3);
  outline: none;
}

/* Responsive utilities */
@media (max-width: 640px) {
  .nebula-suggestion-chip {
    font-size: 0.6125rem; /* 30% smaller than previous 0.875rem */
    padding: 0.375rem 0.5rem; /* Reduced padding proportionally */
    min-height: 36px; /* Reduced from 44px to maintain proportions */
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .chat-container {
    padding-left: 0.5rem;
    padding-right: 0.5rem;
  }
}

/* Touch target improvements for mobile */
@media (max-width: 768px) {
  button, .clickable {
    min-height: 44px;
    min-width: 44px;
  }
}

/* Enhanced button styling */
.theme-button {
  border-radius: var(--radius);
  transition: all 0.2s ease;
}

.theme-button:focus-visible {
  box-shadow: 0 0 0 2px hsl(var(--ring) / 0.4);
}

/* Common hover effect for all interactive elements */
.nebula-hover {
  transition: all 0.15s ease;
}

.nebula-hover:hover {
  background: hsl(var(--hover-bg)) !important;
  border-color: hsl(var(--hover-border)) !important;
  box-shadow: 0 4px 12px hsl(var(--hover-shadow));
  transform: translateY(-1px);
}



/* Specific overrides for input elements in dark theme */
:root input:focus-visible {
  outline: none !important;
  border-color: hsl(var(--primary) / 0.4) !important;
  box-shadow: 0 0 0 2px hsl(var(--ring) / 0.3) !important;
}

/* Button focus-visible overrides */
:root button:focus-visible {
  outline: none !important;
  box-shadow: 0 0 0 2px hsl(var(--ring) / 0.4) !important;
}

/* Light theme focus-visible adjustments */

.light input:focus-visible {
  outline: none !important;
  border-color: hsl(var(--primary) / 0.6) !important;
  box-shadow: 0 0 0 2px hsl(var(--ring) / 0.5) !important;
}

.light button:focus-visible {
  outline: none !important;
  box-shadow: 0 0 0 2px hsl(var(--ring) / 0.6) !important;
}

/* Light scrollbar for textarea */
.textarea-light-scroll::-webkit-scrollbar {
  width: 6px;
}

.textarea-light-scroll::-webkit-scrollbar-track {
  background: transparent;
}

.textarea-light-scroll::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 3px;
  transition: background 0.2s ease;
}

.textarea-light-scroll::-webkit-scrollbar-thumb:hover {
  background: rgba(255, 255, 255, 0.2);
}
